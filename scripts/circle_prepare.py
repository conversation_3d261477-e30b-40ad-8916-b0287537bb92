from collections import defaultdict
import csv
import json

CURRENCY_WHITELIST = [
    "SOL",
    "XRP",
    "BNB",
    "ETH",
    "SUI",
    "BCH",
    "APT",
    "ETC",
    "NEAR",
    "INJ",
    "ADA",
    "LTC",
    "USDT",
    "USDC",
    "DOT",
    "XLM",
    "HBAR",
    "POL",
    "UNI",
    "SEI",
    "BTC",
]
STABLE_CURRENCY_WHITELIST = ["USDT", "USDC"]


def fetch_bidirectional_graph():
    graph = defaultdict(list)
    symbol_price_tick = {}
    with open("bn_margin_trading_pair_with_vol_in_usd.csv", "r") as f:
        reader = csv.reader(f)
        next(reader)  # Skip header row
        for row in reader:
            symbol = row[1]
            base_asset = row[5]
            quote_asset = row[3]
            usd_volume = float(row[2])
            price_tick = float(row[6])
            lot_size = float(row[7])
            min_order_qty = float(row[8])
            min_notional = float(row[9])

            if usd_volume > 0:  # Filter out low volume pairs
                if base_asset not in CURRENCY_WHITELIST or quote_asset not in CURRENCY_WHITELIST:
                    continue
                graph[base_asset].append((quote_asset, symbol, "reverse"))
                graph[quote_asset].append((base_asset, symbol, "forward"))
                symbol_price_tick[symbol] = (price_tick, lot_size, min_order_qty, min_notional)
    return dict(graph), symbol_price_tick


def find_cycles(graph, max_length=3):
    cycles = set()

    def dfs(start, current, path, symbols, directions, visited):
        if len(path) > max_length:
            return
        for neighbor, symbol, direction in graph.get(current, []):
            # if "USDC" in neighbor:
            #     continue
            # if neighbor not in CURRENCY_WHITELIST:
            #     continue
            if neighbor == start and len(path) >= 3:
                cycles.add((tuple(path + [start]), tuple(symbols + [symbol]), tuple(directions + [direction])))
            elif neighbor not in visited:
                dfs(
                    start,
                    neighbor,
                    path + [neighbor],
                    symbols + [symbol],
                    directions + [direction],
                    visited | {neighbor},
                )

    for node in list(graph):
        # if node not in STABLE_CURRENCY_WHITELIST:
        #     continue
        dfs(node, node, [node], [], [], {node})

    return list(cycles)


def main():
    graph, price_tick = fetch_bidirectional_graph()
    cycles = find_cycles(graph)

    print(f"✅ Found {len(cycles)} arbitrage cycles\n")

    json_cycles = []
    for path, symbols, dirs in cycles:
        cycle = []
        start = path[0]
        for i in range(1, len(path)):
            symbol = symbols[i - 1]
            if dirs[i - 1] == "forward":
                symbol = path[i] + "/" + start
            else:
                symbol = start + "/" + path[i]
            start = path[i]
            cycle.append([symbol, dirs[i - 1]])
        json_cycles.append(cycle)
    with open("bn_margin_circles.json", "w") as f:
        json.dump(json_cycles, f, indent=2)

    with open("bn_margin_order_filters.json", "w") as f:
        json.dump(price_tick, f, indent=2)

    unique_symbols = set()
    for _, symbols, _ in cycles:
        for symbol in symbols:
            unique_symbols.add(symbol)
    print(f"✅ Found {len(unique_symbols)} unique symbols")


if __name__ == "__main__":
    main()
