#!/usr/bin/env python3
"""
tri-arb-scanner.py — 根据交易对选择规则筛选三角套利机会的币种
规则：
1. 既有现货交易，又有期货交易
2. 现货交易量要从大到小筛选
3. 同一个币种的quote asset种类越多越好，比如BNB，可以quote by USDT，USDC，BTC，ETH，这种组成的环更多

核心公式
    score = quote_diversity × log10(spot_volume+1) × σ × depth_factor × hit_rate × futures_factor
"""

from __future__ import annotations
import argparse, time, math, sys, traceback
from collections import defaultdict
import json
import os

import requests

BINANCE_SPOT = "https://api.binance.com"
BINANCE_FUTURES = "https://fapi.binance.com"
QUOTE_WHITELIST = {"USDT", "BTC", "ETH", "BNB", "FDUSD", "USDC"}
DEPTH_PRICE_RANGE = 0.0005  # ±0.05 %
MIN_DEPTH_USD = 10_000  # 低于此深度按比例打折

# 缓存配置
CACHE_FILE = "price_tick_cache.json"
FUTURES_CACHE_FILE = "futures_info_cache.json"
CACHE_EXPIRE_HOURS = 24  # 缓存24小时后过期


# ──────────────────────── 通用请求封装 ────────────────────────
def fetch_spot(path: str, retries=5, backoff=5, **params):
    """带重试的现货 API GET。retries 次失败后抛异常"""
    for attempt in range(1, retries + 1):
        try:
            r = requests.get(f"{BINANCE_SPOT}{path}", params=params, timeout=15)  # 15 s 超时
            r.raise_for_status()
            return r.json()
        except Exception as e:
            if attempt == retries:
                raise  # 最后一次仍失败，让上层决定
            wait = backoff * attempt
            print(
                f"    ⚠️ 现货API 调用失败：{e} | 第 {attempt}/{retries} 次重试… " f"等待 {wait} 秒",
                file=sys.stderr,
                flush=True,
            )
            time.sleep(wait)


def fetch_futures(path: str, retries=5, backoff=5, **params):
    """带重试的期货 API GET。retries 次失败后抛异常"""
    for attempt in range(1, retries + 1):
        try:
            r = requests.get(f"{BINANCE_FUTURES}{path}", params=params, timeout=15)  # 15 s 超时
            r.raise_for_status()
            return r.json()
        except Exception as e:
            if attempt == retries:
                raise  # 最后一次仍失败，让上层决定
            wait = backoff * attempt
            print(
                f"    ⚠️ 期货API 调用失败：{e} | 第 {attempt}/{retries} 次重试… " f"等待 {wait} 秒",
                file=sys.stderr,
                flush=True,
            )
            time.sleep(wait)


# 简单包装原有接口
exchange_info = lambda **kw: fetch_spot("/api/v3/exchangeInfo", **kw)["symbols"]
futures_exchange_info = lambda **kw: fetch_futures("/fapi/v1/exchangeInfo", **kw)["symbols"]
stats24 = lambda **kw: fetch_spot("/api/v3/ticker/24hr", **kw)
futures_stats24 = lambda **kw: fetch_futures("/fapi/v1/ticker/24hr", **kw)
depth_snapshot = lambda symbol, **kw: fetch_spot("/api/v3/depth", symbol=symbol, limit=1000, **kw)


def klines(symbol: str, start_ms: int, end_ms: int, sleep_sec: float, retries: int, backoff: int):
    """拉取 [start_ms, end_ms) 的 1 min K 线，内部也带重试"""
    out = []
    while start_ms < end_ms:
        batch = fetch_spot(
            "/api/v3/klines",
            symbol=symbol,
            interval="1m",
            startTime=start_ms,
            endTime=min(end_ms, start_ms + 1000 * 60_000),
            limit=1000,
            retries=retries,
            backoff=backoff,
        )
        if not batch:
            break
        out.extend(float(c[4]) for c in batch)
        start_ms = batch[-1][0] + 60_000
        time.sleep(sleep_sec)
    return out


# ────────────────────────── 工具函数 ──────────────────────────
def usd_depth(snapshot, mid, usd_per_quote=1.0):
    rng = DEPTH_PRICE_RANGE
    bid_sum = 0.0
    for price, qty in snapshot["bids"]:
        p = float(price)
        if p < mid * (1 - rng):
            break
        bid_sum += float(qty) * p * usd_per_quote
    ask_sum = 0.0
    for price, qty in snapshot["asks"]:
        p = float(price)
        if p > mid * (1 + rng):
            break
        ask_sum += float(qty) * p * usd_per_quote
    return min(bid_sum, ask_sum)


def stdev(vals):
    n = len(vals)
    if n < 2:
        return 0.0
    m = sum(vals) / n
    return (sum((x - m) ** 2 for x in vals) / (n - 1)) ** 0.5


# ────────────────────────── 缓存管理 ──────────────────────────
def load_price_tick_cache():
    """加载 price_tick 占比缓存"""
    if not os.path.exists(CACHE_FILE):
        return {}

    try:
        with open(CACHE_FILE, "r") as f:
            cache_data = json.load(f)

        # 检查缓存是否过期
        cache_time = cache_data.get("timestamp", 0)
        current_time = time.time()
        if current_time - cache_time > CACHE_EXPIRE_HOURS * 3600:
            print(f">>> 缓存已过期 ({CACHE_EXPIRE_HOURS}小时)，将重新获取数据")
            return {}

        print(f">>> 加载缓存数据，包含 {len(cache_data.get('ratios', {}))} 个交易对")
        return cache_data.get("ratios", {})
    except Exception as e:
        print(f">>> 加载缓存失败: {e}，将重新获取数据")
        return {}


def save_price_tick_cache(ratios):
    """保存 price_tick 占比缓存"""
    try:
        cache_data = {"timestamp": time.time(), "ratios": ratios}
        with open(CACHE_FILE, "w") as f:
            json.dump(cache_data, f, indent=2)
        print(f">>> 已保存 {len(ratios)} 个交易对的 price_tick 占比到缓存")
    except Exception as e:
        print(f">>> 保存缓存失败: {e}")


def load_futures_cache():
    """加载期货信息缓存"""
    if not os.path.exists(FUTURES_CACHE_FILE):
        return {}

    try:
        with open(FUTURES_CACHE_FILE, "r") as f:
            cache_data = json.load(f)

        # 检查缓存是否过期
        cache_time = cache_data.get("timestamp", 0)
        current_time = time.time()
        if current_time - cache_time > CACHE_EXPIRE_HOURS * 3600:
            print(f">>> 期货缓存已过期 ({CACHE_EXPIRE_HOURS}小时)，将重新获取数据")
            return {}

        print(f">>> 加载期货缓存数据，包含 {len(cache_data.get('futures_symbols', set()))} 个期货交易对")
        return cache_data
    except Exception as e:
        print(f">>> 加载期货缓存失败: {e}，将重新获取数据")
        return {}


def save_futures_cache(futures_symbols, futures_volumes):
    """保存期货信息缓存"""
    try:
        cache_data = {
            "timestamp": time.time(),
            "futures_symbols": list(futures_symbols),
            "futures_volumes": futures_volumes,
        }
        with open(FUTURES_CACHE_FILE, "w") as f:
            json.dump(cache_data, f, indent=2)
        print(f">>> 已保存 {len(futures_symbols)} 个期货交易对信息到缓存")
    except Exception as e:
        print(f">>> 保存期货缓存失败: {e}")


# ────────────────────────── 主流程 ────────────────────────────
def main():
    p = argparse.ArgumentParser()
    p.add_argument("--threshold", type=float, default=0.001, help="最小可执行价差 (0.001 = 0.1%%)")
    p.add_argument("--days", type=int, default=7)
    p.add_argument("--limit", type=int, default=100, help="先用成交额粗筛的币数")
    p.add_argument("--top", type=int, default=100)
    p.add_argument("--sleep", type=float, default=0.08, help="每 1000 根 kline 拉完后的 sleep 秒数")
    p.add_argument("--retries", type=int, default=5, help="API 调用最大重试次数")
    p.add_argument("--backoff", type=int, default=5, help="第一次重试等待秒数 (随后指数增加)")
    p.add_argument("--refresh-cache", action="store_true", help="强制刷新缓存")
    args = p.parse_args()
    # 固定使用最近 7 天窗口（忽略传入的 --days）
    args.days = 7

    print(">>> 拉取现货交易所元数据 …")
    spot_symbols = exchange_info(retries=args.retries, backoff=args.backoff)
    print(f">>> 获取到 {len(spot_symbols)} 个现货交易对")

    print(">>> 拉取期货交易所元数据 …")
    # 加载期货缓存
    if args.refresh_cache:
        print(">>> 强制刷新期货缓存模式")
        futures_cache = {}
    else:
        futures_cache = load_futures_cache()

    if not futures_cache:
        print(">>> 从API获取期货数据 …")
        futures_symbols_raw = futures_exchange_info(retries=args.retries, backoff=args.backoff)
        futures_volumes_raw = futures_stats24(retries=args.retries, backoff=args.backoff)

        # 提取永续合约的base asset
        futures_symbols = set()
        futures_volumes = {}
        for s in futures_symbols_raw:
            if s["status"] == "TRADING" and s["contractType"] == "PERPETUAL":
                base_asset = s["baseAsset"]
                futures_symbols.add(base_asset)

        for v in futures_volumes_raw:
            if v["symbol"].endswith("USDT"):  # 只关注USDT计价的期货
                base_asset = v["symbol"].replace("USDT", "")
                if base_asset in futures_symbols:
                    futures_volumes[base_asset] = float(v["quoteVolume"])

        save_futures_cache(futures_symbols, futures_volumes)
        print(f">>> 获取到 {len(futures_symbols)} 个期货base asset")
    else:
        futures_symbols = set(futures_cache.get("futures_symbols", []))
        futures_volumes = futures_cache.get("futures_volumes", {})
        print(f">>> 从缓存加载 {len(futures_symbols)} 个期货base asset")

    print(">>> 开始筛选既有现货又有期货的币种 …")
    base_to_quotes, sym_name = defaultdict(set), {}

    for s in spot_symbols:
        if s["status"] != "TRADING":
            continue
        b, q = s["baseAsset"], s["quoteAsset"]
        # 规则1：只保留既有现货又有期货的币种
        if b in futures_symbols:
            base_to_quotes[b].add(q)
            sym_name[(b, q)] = s["symbol"]

    print(f">>> 筛选后剩余 {len(base_to_quotes)} 个既有现货又有期货的币种")

    print(">>> 拉取现货 24h 成交额 …")
    spot_vol_map = {s["symbol"]: float(s["quoteVolume"]) for s in stats24(retries=args.retries, backoff=args.backoff)}

    # ① 粗筛：根据新规则筛选
    # 规则2：现货交易量要从大到小筛选
    # 规则3：同一个币种的quote asset种类越多越好
    cand = []
    for base, qs in base_to_quotes.items():
        usable = qs & QUOTE_WHITELIST
        if len(usable) < 2:
            continue

        # 计算quote asset多样性得分（规则3）
        quote_diversity = len(usable)

        # 计算存在交叉市场的报价币对数（三环数）
        tri_pairs = []
        ulist = sorted(list(usable))
        for i in range(len(ulist)):
            for j in range(i + 1, len(ulist)):
                q1, q2 = ulist[i], ulist[j]
                if (q1, q2) in sym_name or (q2, q1) in sym_name:
                    tri_pairs.append((q1, q2))
        tri_count = len(tri_pairs)
        if tri_count == 0:
            continue

        # 计算现货总交易量（规则2）
        spot_vol_sum = sum(spot_vol_map.get(sym_name[(base, q)], 0) for q in usable)

        # 计算期货交易量因子
        futures_vol = futures_volumes.get(base, 0)
        futures_factor = math.log10(futures_vol + 1) if futures_vol > 0 else 0

        # 综合评分：quote多样性 × 现货交易量 × 期货因子
        basic_score = quote_diversity * math.log10(spot_vol_sum + 1) * (1 + futures_factor * 0.1)

        cand.append((base, usable, spot_vol_sum, basic_score, tri_pairs, quote_diversity, futures_vol))

    # 按综合评分排序（规则2：交易量从大到小）
    cand.sort(key=lambda x: x[3], reverse=True)
    cand = cand[: args.limit]
    print(f">>> 第一轮筛选后深度分析 {len(cand)} 个币种")

    end_ms = int(time.time() * 1000)
    start_ms = end_ms - args.days * 24 * 60 * 60 * 1000
    # 构建部分常见报价币到 USD 的估值（用于将深度换算到 USD）
    quote_usd = {"USDT": 1.0, "USDC": 1.0, "FDUSD": 1.0}
    # 获取 BTC、ETH、BNB 的 USD 价格
    for sym, name in [("BTCUSDT", "BTC"), ("ETHUSDT", "ETH"), ("BNBUSDT", "BNB")]:
        try:
            quote_usd[name] = float(
                fetch_spot("/api/v3/ticker/price", symbol=sym, retries=args.retries, backoff=args.backoff)["price"]
            )
        except Exception:
            pass

    results = []
    for idx, (base, qs, spot_vol_sum, basic_score, tri_pairs, quote_diversity, futures_vol) in enumerate(cand, 1):
        print(
            f"[{idx:02}/{len(cand)}] 处理 {base} (现货量:{spot_vol_sum/1e6:.1f}M, 期货量:{futures_vol/1e6:.1f}M, Quote多样性:{quote_diversity}) …",
            flush=True,
        )

        # 该 base 的三环数量与“每币可产环数”效率
        tri_count = len(tri_pairs)
        coins_count = 1 + len(qs)

        # 本 base 级别缓存，避免重复请求
        kline_cache = {}
        depth_cache = {}

        for q1, q2 in tri_pairs:
            leg1 = sym_name[(base, q1)]
            leg2 = sym_name[(base, q2)]
            # 交叉腿必然存在（由 tri_pairs 构建时保证）
            leg3 = sym_name.get((q1, q2)) or sym_name.get((q2, q1))

            # --- 2.1 K 线 -----------------------------------------------------
            try:
                if leg1 in kline_cache:
                    p_b_q1 = kline_cache[leg1]
                else:
                    p_b_q1 = klines(leg1, start_ms, end_ms, args.sleep, args.retries, args.backoff)
                    kline_cache[leg1] = p_b_q1
                if leg2 in kline_cache:
                    p_b_q2 = kline_cache[leg2]
                else:
                    p_b_q2 = klines(leg2, start_ms, end_ms, args.sleep, args.retries, args.backoff)
                    kline_cache[leg2] = p_b_q2
                if leg3 in kline_cache:
                    p_cross = kline_cache[leg3]
                else:
                    p_cross = klines(leg3, start_ms, end_ms, args.sleep, args.retries, args.backoff)
                    kline_cache[leg3] = p_cross
            except Exception as e:
                print(f"    ❌ kline 多次失败：{e}")
                continue

            m = min(len(p_b_q1), len(p_b_q2), len(p_cross))
            if m < 200:
                print("    ⚠️ 数据点不足 (<200)，跳过该三环")
                continue

            # 判断 leg3 的方向：q1/q2 还是 q2/q1
            if leg3.startswith(q1):  # q1q2, 价格= q1 in q2
                price_q1_q2 = p_cross
            else:  # q2q1, 先取倒数
                price_q1_q2 = [1 / x for x in p_cross]

            # 计算 spread（取绝对值，这样正/反方向都算）
            spreads = [abs(p_b_q1[i] / (p_b_q2[i] * price_q1_q2[i]) - 1) for i in range(m)]
            sigma = stdev(spreads)  # 周期固定 7 天，sigma 代表周内三角套利价差波动率
            hits = sum(1 for x in spreads if x > args.threshold)
            hit_rate = hits / m
            if hit_rate == 0:
                # 该三环从未超过阈值，跳过
                continue

            # 2.2 深度
            try:
                if leg1 in depth_cache:
                    d1 = depth_cache[leg1]
                else:
                    d1 = depth_snapshot(leg1, retries=args.retries, backoff=args.backoff)
                    depth_cache[leg1] = d1
                if leg2 in depth_cache:
                    d2 = depth_cache[leg2]
                else:
                    d2 = depth_snapshot(leg2, retries=args.retries, backoff=args.backoff)
                    depth_cache[leg2] = d2
                if leg3 in depth_cache:
                    d3 = depth_cache[leg3]
                else:
                    d3 = depth_snapshot(leg3, retries=args.retries, backoff=args.backoff)
                    depth_cache[leg3] = d3
            except Exception as e:
                print(f"    ❌ depth 多次失败：{e}")
                traceback.print_exc(limit=1)
                continue
            mid1 = (float(d1["bids"][0][0]) + float(d1["asks"][0][0])) / 2
            mid2 = (float(d2["bids"][0][0]) + float(d2["asks"][0][0])) / 2
            mid3 = (float(d3["bids"][0][0]) + float(d3["asks"][0][0])) / 2
            # 将三条腿深度统一换算为 USD
            usd_per_q1 = quote_usd.get(q1, 1.0)
            usd_per_q2 = quote_usd.get(q2, 1.0)
            # 交叉腿的计价币是 leg3 的报价币；根据符号推断：若 leg3 == q1q2，则价格单位为 q2；若 leg3 == q2q1，则为 q1
            cross_quote = q2 if sym_name.get((q1, q2)) else q1
            usd_per_cross_quote = quote_usd.get(cross_quote, 1.0)

            dep1 = usd_depth(d1, mid1, usd_per_quote=usd_per_q1)
            dep2 = usd_depth(d2, mid2, usd_per_quote=usd_per_q2)
            dep3 = usd_depth(d3, mid3, usd_per_quote=usd_per_cross_quote)
            min_dep = min(dep1, dep2, dep3)
            depth_factor = min(1.0, min_dep / MIN_DEPTH_USD)

            # 新的评分公式：考虑quote多样性、现货交易量、期货因子
            futures_factor = math.log10(futures_vol + 1) if futures_vol > 0 else 0
            quote_diversity_factor = quote_diversity / len(QUOTE_WHITELIST)  # 标准化

            # 综合评分：quote多样性 × 现货交易量 × 期货因子 × 技术指标
            score = (
                quote_diversity_factor
                * math.log10(spot_vol_sum + 1)
                * (1 + futures_factor * 0.2)
                * sigma
                * depth_factor
                * hit_rate
            )

            results.append(
                {
                    "base": base,
                    "tri_count": tri_count,
                    "coins": coins_count,
                    "spot_vol": spot_vol_sum,
                    "futures_vol": futures_vol,
                    "quote_diversity": quote_diversity,
                    "sigma": sigma,
                    "hit": hit_rate,
                    "depth": min_dep,
                    "score": score,
                    "triangle": (leg1, leg2, leg3),
                }
            )

    # ③ 输出
    results.sort(key=lambda r: r["score"], reverse=True)
    top = results[: args.top]
    print("\n=========== 最终结果（基于新规则筛选） ===========")
    print(f"规则: 1.既有现货又有期货 2.现货交易量从大到小 3.Quote多样性越高越好")
    print(f"阈值: {args.threshold:.3%} | 窗口: {args.days} d | 输出: Top {args.top}\n")

    header = "{:<8} {:>6} {:>5} {:>10} {:>10} {:>6} {:>8} {:>7} {:>9} {:>9}".format(
        "Base", "TriCnt", "Coin#", "SpotVolM", "FutVolM", "QDiv", "σ(week)", "Hit%", "Depth$", "Score"
    )
    print(header)
    print("-" * len(header))

    for r in top:
        print(
            "{:<8} {:>6} {:>5} {:>10.1f} {:>10.1f} {:>6} {:>8.4f} {:>7.2%} {:>9.0f} {:>9.2f}".format(
                r["base"],
                r.get("tri_count", 0),
                r.get("coins", 0),
                r["spot_vol"] / 1_000_000,
                r["futures_vol"] / 1_000_000,
                r["quote_diversity"],
                r["sigma"],
                r["hit"],
                r["depth"],
                r["score"],
            )
        )
        print("    ", " ↔ ".join(r["triangle"]))

    print(f"\n筛选总结:")
    print(f"- 总现货交易对: {len(spot_symbols)}")
    print(f"- 期货base asset: {len(futures_symbols)}")
    print(f"- 既有现货又有期货: {len(base_to_quotes)}")
    print(f"- 最终分析的币种: {len(cand)}")
    print(f"- 有套利机会的三角: {len(results)}")
    print("\n全部完成。")


if __name__ == "__main__":
    main()
