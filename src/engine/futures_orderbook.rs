//! Ultra‑low‑latency stack‑allocated Binance Futures order book implementation.
//!
//! * Completely allocation‑free after start‑up: no Vec, HashMap or heap at runtime.
//! * Suitable for `#![no_std]` environments (you can disable the few debug assertions).
//! * O(log N) search, O(N) insert/delete with `ptr::copy` memcpy‑style shifts.
//! * Best for depths up to a few thousand levels (N <= 5000) which covers Binance full book.
//! * Optimized for futures trading with liquidation price considerations.
//!
//! Adapted from Binance "How to manage a local order book correctly" steps — see developers.binance.com.
//!
//! Compile with: `cargo build --release` (requires Rust 1.77+ for const‑generics).

use core::cmp::Ordering;
use core::ptr;
use std::cmp::max;
use std::fmt::Display;

use crate::encoding::book_ticker::FuturesBookTicker;
use crate::encoding::futures_orderbook::{FuturesOrderbookSnapshot, FuturesOrderbookUpdate};
use crate::engine::futures_const::FUTURES_MAKER_MIN_LEVEL;

/// A single futures order book level (price, quantity).
#[derive(Clone, Copy, Debug, Default)]
#[repr(C)]
pub struct FuturesLevel {
    pub price: f64,
    pub qty: f64,
}

/// A stack‑allocated futures side (bid or ask).
///
/// * `N` : maximum number of levels to keep.
/// * `IS_BID` : `true` for bids (sorted DESC), `false` for asks (ASC).
#[derive(Clone, Copy, Debug)]
pub struct FuturesSide<const N: usize, const IS_BID: bool> {
    levels: [FuturesLevel; N],
    len: usize,
}

pub enum ImbalanceType {
    BidMore(f64),
    AskMore(f64),
    Balanced,
}

#[derive(Clone, Copy, Debug, PartialEq, Eq)]
pub enum BookSide {
    Bid,
    Ask,
}

impl<const N: usize, const IS_BID: bool> FuturesSide<N, IS_BID> {
    /// Empty side.
    #[inline]
    pub const fn new() -> Self {
        Self {
            levels: [FuturesLevel {
                price: 0.0,
                qty: 0.0,
            }; N],
            len: 0,
        }
    }

    /// Binary search for a price. Returns (found, index).
    #[inline(always)]
    fn find(&self, price: f64) -> (bool, usize) {
        let mut low = 0usize;
        let mut high = self.len;
        while low < high {
            let mid = (low + high) >> 1;
            let mid_price = unsafe { self.levels.get_unchecked(mid).price };
            let ord = if IS_BID {
                if price > mid_price {
                    Ordering::Less
                } else if price < mid_price {
                    Ordering::Greater
                } else {
                    Ordering::Equal
                }
            } else {
                if price < mid_price {
                    Ordering::Less
                } else if price > mid_price {
                    Ordering::Greater
                } else {
                    Ordering::Equal
                }
            };

            match ord {
                Ordering::Less => high = mid,
                Ordering::Greater => low = mid + 1,
                Ordering::Equal => return (true, mid),
            }
        }
        (false, low)
    }

    /// Find the nearest level to the target price. Returns (index, level).
    /// If the orderbook is empty, returns None.
    /// For bid side: if price > best_bid + 0.02%, returns None.
    /// For ask side: if price < best_ask - 0.02%, returns None.
    #[inline(always)]
    fn find_nearest(&self, price: f64) -> Option<(usize, &FuturesLevel)> {
        if self.len == 0 {
            return None;
        }

        // Check 0.02% threshold against the best price (1st level)
        let best_level = unsafe { self.levels.get_unchecked(0) };
        let best_price = best_level.price;
        let threshold = best_price * 0.0002; // 0.02% = 0.0002

        if IS_BID {
            // For bid side: reject if target price > best_bid + 0.02%
            if price > best_price + threshold {
                return None;
            }
        } else {
            // For ask side: reject if target price < best_ask - 0.02%
            if price < best_price - threshold {
                return None;
            }
        }

        let (found, idx) = self.find(price);

        if found {
            // Exact match found
            return Some((idx, unsafe { self.levels.get_unchecked(idx) }));
        }

        // No exact match, find the nearest level
        if idx == 0 {
            // Target price is before the first level
            Some((0, unsafe { self.levels.get_unchecked(0) }))
        } else if idx >= self.len {
            // Target price is after the last level
            let last_idx = self.len - 1;
            Some((last_idx, unsafe { self.levels.get_unchecked(last_idx) }))
        } else {
            // Target price is between two levels, find the closer one
            let prev_level = unsafe { self.levels.get_unchecked(idx - 1) };
            let next_level = unsafe { self.levels.get_unchecked(idx) };

            let prev_distance = (price - prev_level.price).abs();
            let next_distance = (price - next_level.price).abs();

            if prev_distance <= next_distance {
                Some((idx - 1, prev_level))
            } else {
                Some((idx, next_level))
            }
        }
    }

    /// Insert/update level. `qty == 0` removes.
    #[inline]
    pub fn upsert(&mut self, price: f64, qty: f64) {
        let (found, idx) = self.find(price);

        if found {
            if qty == 0.0 {
                // delete
                unsafe {
                    let dst = self.levels.as_mut_ptr().add(idx);
                    let src = dst.add(1);
                    ptr::copy(src, dst, self.len - idx - 1);
                }
                self.len -= 1;
            } else {
                unsafe {
                    self.levels.get_unchecked_mut(idx).qty = qty;
                }
            }
        } else if qty != 0.0 {
            // Check capacity to prevent array bounds overflow
            if self.len >= N {
                // For a 20-depth orderbook, discard levels beyond capacity
                // In a real trading system, you might want to:
                // 1. Replace the worst level if this is a better price
                // 2. Log a warning about dropped levels
                // 3. Implement a more sophisticated capacity management strategy
                return;
            }

            unsafe {
                let dst = self.levels.as_mut_ptr().add(idx + 1);
                let src = self.levels.as_ptr().add(idx);
                ptr::copy(src, dst, self.len - idx); // shift right
                let level = self.levels.get_unchecked_mut(idx);
                level.price = price;
                level.qty = qty;
            }
            self.len += 1;
        }
    }

    #[inline]
    pub fn iter(&self) -> impl Iterator<Item = &FuturesLevel> {
        self.levels[..self.len].iter()
    }

    pub fn enumerate(&self) -> impl Iterator<Item = (usize, &FuturesLevel)> {
        self.levels[..self.len].iter().enumerate()
    }
}

#[derive(Clone, Copy, Debug, PartialEq, Eq)]
pub enum OrderBookUpdateBy {
    Snapshot,
    Diff,
    Uninited,
}

/// Complete futures order book (bids + asks) with monotonic update sequence tracking.
#[derive(Clone)]
pub struct FuturesOrderBook<const N: usize> {
    pub first_update_id: u64,
    pub final_update_id: u64,
    pub prev_update_id: u64,
    pub event_time: u64,
    pub transaction_time: u64,
    bids: FuturesSide<N, true>,
    asks: FuturesSide<N, false>,
    pub book_ticker: Option<FuturesBookTicker>,
    pub best_bid: Option<FuturesLevel>,
    pub best_ask: Option<FuturesLevel>,
    pub update_by: OrderBookUpdateBy,
    pub diff_cache: Vec<FuturesOrderbookUpdate>,
}

impl<const N: usize> FuturesOrderBook<N> {
    /// Empty book.
    pub const fn new() -> Self {
        Self {
            first_update_id: 0,
            final_update_id: 0,
            prev_update_id: 0,
            event_time: 0,
            transaction_time: 0,
            bids: FuturesSide::new(),
            asks: FuturesSide::new(),
            book_ticker: None,
            best_bid: None,
            best_ask: None,
            update_by: OrderBookUpdateBy::Uninited,
            diff_cache: Vec::new(),
        }
    }

    fn check_overlap(&self) {
        if self.bids.len > 0 && self.asks.len > 0 {
            if self.bids.levels[0].price >= self.asks.levels[0].price {
                panic!("Bids and asks overlap: {}", self);
            }
        }
    }

    /// Apply REST snapshot.
    pub fn apply_snapshot(&mut self, snapshot: &FuturesOrderbookSnapshot) -> bool {
        if snapshot.update_id <= self.final_update_id {
            return false;
        }
        self.final_update_id = snapshot.update_id;
        self.first_update_id = snapshot.update_id;
        self.event_time = snapshot.event_time;
        self.transaction_time = snapshot.transaction_time;
        self.bids.len = 0;
        self.asks.len = 0;

        for &(p, q) in snapshot.bids.iter().take(N) {
            if p == 0.0 {
                break;
            }
            self.bids.upsert(p, q);
        }
        for &(p, q) in snapshot.asks.iter().take(N) {
            if p == 0.0 {
                break;
            }
            self.asks.upsert(p, q);
        }
        self.check_overlap();
        self.update_bbo_inner();
        self.update_by = OrderBookUpdateBy::Snapshot;
        crate::debug!("apply_snapshot: {}", self);
        true
    }

    pub fn apply_diff(&mut self, diff: &FuturesOrderbookUpdate) -> bool {
        if self.update_by == OrderBookUpdateBy::Uninited {
            self.diff_cache.push(diff.clone());
            return false;
        }
        if self.diff_cache.len() > 0 {
            let mut applyed = false;
            let cloned_diffs = self.diff_cache.clone();
            for item in cloned_diffs.iter() {
                if self.apply_diff_inner(item) {
                    applyed = true;
                }
            }
            if applyed {
                self.diff_cache.clear();
                self.apply_diff_inner(diff)
            } else {
                self.diff_cache.push(diff.clone());
                false
            }
        } else {
            self.apply_diff_inner(diff)
        }
    }

    fn apply_diff_inner(&mut self, diff: &FuturesOrderbookUpdate) -> bool {
        match self.update_by {
            OrderBookUpdateBy::Snapshot => {
                if !(diff.first_update_id <= self.final_update_id
                    && diff.final_update_id >= self.final_update_id)
                {
                    crate::info!(
                        "snapshot diff not updated: {} {}",
                        diff.final_update_id,
                        self.final_update_id
                    );
                    return false;
                }
            }
            OrderBookUpdateBy::Diff => {
                if diff.prev_update_id != self.final_update_id {
                    crate::info!(
                        "snapshot diff not updated: {} {}",
                        diff.prev_update_id,
                        self.final_update_id
                    );
                    return false;
                }
            }
            OrderBookUpdateBy::Uninited => {
                crate::info!("snapshot diff not updated: uninited");
                return false;
            }
        };
        self.update_by = OrderBookUpdateBy::Diff;
        self.final_update_id = diff.final_update_id;
        self.first_update_id = diff.first_update_id;
        self.prev_update_id = diff.prev_update_id;
        self.event_time = diff.event_time;
        self.transaction_time = diff.transaction_time;
        for &(p, q) in diff.bid_updates.iter() {
            if p == 0.0 {
                break;
            }
            self.bids.upsert(p, q);
        }
        for &(p, q) in diff.ask_updates.iter() {
            if p == 0.0 {
                break;
            }
            self.asks.upsert(p, q);
        }

        self.check_overlap();
        self.update_bbo_inner();
        crate::debug!("apply_diff_inner: {}", self);
        true
    }

    pub fn apply_book_ticker(&mut self, book_ticker: &FuturesBookTicker) -> bool {
        if self.final_update_id > book_ticker.update_id {
            return false;
        }
        self.book_ticker = Some(book_ticker.clone());
        self.update_bbo_inner();
        true
    }

    #[inline]
    pub fn best_bid(&self) -> Option<&FuturesLevel> {
        self.best_bid.as_ref()
    }

    #[inline]
    pub fn best_ask(&self) -> Option<&FuturesLevel> {
        self.best_ask.as_ref()
    }

    #[inline]
    pub fn spread_in_bp(&self) -> f64 {
        if self.bids.len > 0 && self.asks.len > 0 {
            (self.asks.levels[0].price - self.bids.levels[0].price)
                / ((self.bids.levels[0].price + self.asks.levels[0].price) / 2.0)
                * 10000.0
        } else {
            0.0
        }
    }

    #[inline]
    pub fn find_ask_level(&self, price: f64) -> Option<(usize, &FuturesLevel)> {
        let (found, idx) = self.asks.find(price);
        if found {
            Some((idx, &self.asks.levels[idx]))
        } else {
            None
        }
    }

    #[inline]
    pub fn find_bid_level(&self, price: f64) -> Option<(usize, &FuturesLevel)> {
        let (found, idx) = self.bids.find(price);
        if found {
            Some((idx, &self.bids.levels[idx]))
        } else {
            None
        }
    }

    /// Find the nearest bid level to the target price.
    #[inline]
    pub fn find_nearest_bid_level(&self, price: f64) -> Option<(usize, &FuturesLevel)> {
        self.bids.find_nearest(price)
    }

    /// Find the nearest ask level to the target price.
    #[inline]
    pub fn find_nearest_ask_level(&self, price: f64) -> Option<(usize, &FuturesLevel)> {
        self.asks.find_nearest(price)
    }

    #[inline]
    pub fn asks_amount_at(&self, level: usize) -> f64 {
        if self.asks.len >= level + 1 {
            self.asks.levels[level].qty * self.asks.levels[level].price
        } else {
            0.0
        }
    }

    #[inline]
    pub fn bids_amount_at(&self, level: usize) -> f64 {
        if self.bids.len >= level + 1 {
            self.bids.levels[level].qty * self.bids.levels[level].price
        } else {
            0.0
        }
    }

    #[inline]
    pub fn bid_count(&self) -> usize {
        self.bids.len
    }

    #[inline]
    pub fn ask_count(&self) -> usize {
        self.asks.len
    }

    /// Calculate liquidation price based on leverage and position size
    #[inline]
    pub fn calculate_liquidation_price(
        &self,
        entry_price: f64,
        leverage: f64,
        position_size: f64,
        is_long: bool,
    ) -> f64 {
        if leverage <= 0.0 || position_size <= 0.0 {
            return 0.0;
        }

        let maintenance_margin = 0.005; // 0.5% maintenance margin (can be configurable)
        let liquidation_threshold = maintenance_margin / leverage;

        if is_long {
            entry_price * (1.0 - liquidation_threshold)
        } else {
            entry_price * (1.0 + liquidation_threshold)
        }
    }

    pub fn find_price_by_qty(&self, qty: f64, side: BookSide, min_level: usize) -> Option<f64> {
        let levels = match side {
            BookSide::Bid => self.bids.levels,
            BookSide::Ask => self.asks.levels,
        };
        let mut total_qty = 0.0;
        let threshold = qty * 0.9;
        crate::debug!(
            "find_price_by_qty: qty: {}, side: {:?}, min_level: {}",
            qty,
            side,
            min_level
        );
        for (i, level) in levels.iter().enumerate() {
            total_qty += level.qty;
            crate::debug!(
                "find_price_by_qty: total_qty: {}, threshold: {}, i: {}, level: {}",
                total_qty,
                threshold,
                i,
                level.price
            );
            if total_qty >= threshold && i > min_level {
                let final_price = match side {
                    BookSide::Bid => level.price + 0.2,
                    BookSide::Ask => level.price - 0.2,
                };
                return Some(final_price);
            }
        }
        None
    }

    pub fn is_imbalance(&self) -> ImbalanceType {
        if self.best_bid.is_none() || self.best_ask.is_none() {
            return ImbalanceType::Balanced;
        }
        let imbalance = self.find_max_level_with_cumulative_imbalance();
        if imbalance.is_none() {
            return ImbalanceType::Balanced;
        }
        let (level, total_bid_qty, total_ask_qty) = imbalance.unwrap();
        crate::debug!(
            "is_imbalance: level: {}, total_bid_qty: {}, total_ask_qty: {}",
            level,
            total_bid_qty,
            total_ask_qty
        );
        if level < FUTURES_MAKER_MIN_LEVEL {
            return ImbalanceType::Balanced;
        }

        if total_bid_qty > total_ask_qty {
            ImbalanceType::BidMore(total_ask_qty)
        } else if total_ask_qty > total_bid_qty {
            ImbalanceType::AskMore(total_bid_qty)
        } else {
            ImbalanceType::Balanced
        }
    }

    pub fn find_thinest_level_at_side(&self, side: BookSide) -> Option<(usize, &FuturesLevel)> {
        match side {
            BookSide::Bid => self
                .bids
                .enumerate()
                .min_by(|a, b| a.1.qty.partial_cmp(&b.1.qty).unwrap()),
            BookSide::Ask => self
                .asks
                .enumerate()
                .min_by(|a, b| a.1.qty.partial_cmp(&b.1.qty).unwrap()),
        }
    }

    pub fn find_place_level_in_side(&self, side: BookSide) -> Option<f64> {
        let levels = match side {
            BookSide::Bid => self.bids.levels,
            BookSide::Ask => self.asks.levels,
        };

        // 检查是否有档位数据
        let len = match side {
            BookSide::Bid => self.bids.len,
            BookSide::Ask => self.asks.len,
        };

        if len == 0 || len == 1 {
            return None;
        }

        // 从第一个实际档位开始
        let mut initial_price = levels[0].price;

        // 从第二个档位开始遍历
        for i in 1..len {
            let level = &levels[i];

            if (level.price - initial_price).abs() > 0.11 {
                return Some(level.price);
            }

            initial_price = level.price;
            if i > 30 {
                return Some(level.price);
            }
        }
        None
    }

    /// Find the maximum level where the cumulative quantity difference between bids and asks exceeds 5.
    /// Returns the level index and the cumulative quantities at that level.
    pub fn find_max_level_with_cumulative_imbalance(&self) -> Option<(usize, f64, f64)> {
        if self.bids.len == 0 || self.asks.len == 0 {
            return None;
        }

        let max_levels = std::cmp::min(self.bids.len, self.asks.len);

        // Use sliding window to avoid repeated calculations
        let mut cumulative_bid_qty = 0.0;
        let mut cumulative_ask_qty = 0.0;
        let mut max_level_with_imbalance: Option<(usize, f64, f64)> = None;

        for level in 0..max_levels {
            // Add current level quantities to cumulative totals
            cumulative_bid_qty += self.bids.levels[level].qty;
            cumulative_ask_qty += self.asks.levels[level].qty;

            let ratio = cumulative_bid_qty / cumulative_ask_qty;
            if ratio > 3.0 || ratio < 0.33 {
                // Update the maximum level found so far
                max_level_with_imbalance = Some((level, cumulative_bid_qty, cumulative_ask_qty));
            } else {
                break;
            }
        }
        max_level_with_imbalance
    }

    pub fn mid_price(&self) -> f64 {
        if self.best_bid.is_none() || self.best_ask.is_none() {
            return 0.0;
        }
        (self.best_bid.unwrap().price + self.best_ask.unwrap().price) / 2.0
    }

    fn update_bbo_inner(&mut self) {
        match self.book_ticker {
            Some(ref book_ticker) => {
                if self.final_update_id > book_ticker.update_id {
                    self.best_bid = Some(FuturesLevel {
                        price: self.bids.levels[0].price,
                        qty: self.bids.levels[0].qty,
                    });
                    self.best_ask = Some(FuturesLevel {
                        price: self.asks.levels[0].price,
                        qty: self.asks.levels[0].qty,
                    });
                } else {
                    self.best_bid = Some(FuturesLevel {
                        price: book_ticker.bid_price,
                        qty: book_ticker.bid_qty,
                    });
                    self.best_ask = Some(FuturesLevel {
                        price: book_ticker.ask_price,
                        qty: book_ticker.ask_qty,
                    });
                }
            }
            None => {
                self.best_bid = Some(FuturesLevel {
                    price: self.bids.levels[0].price,
                    qty: self.bids.levels[0].qty,
                });
                self.best_ask = Some(FuturesLevel {
                    price: self.asks.levels[0].price,
                    qty: self.asks.levels[0].qty,
                });
            }
        }
    }
}

impl<const N: usize> Display for FuturesOrderBook<N> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Futures Orderbook Snapshot: \n").unwrap();
        write!(
            f,
            "\nFirst Update ID: {}\nFinal Update ID: {}\nPrev Update ID: {}\nEvent Time: {}\nTransaction Time: {}\n",
            self.first_update_id,
            self.final_update_id,
            self.prev_update_id,
            self.event_time,
            self.transaction_time
        )
        .unwrap();

        let max_len = max(self.bids.len, self.asks.len);
        for i in 0..max_len {
            if i < self.bids.len {
                let level = &self.bids.levels[i];
                write!(f, "    Bid: {}@{}\t\t", level.price, level.qty).unwrap();
            } else {
                write!(f, "\t\t\t\t").unwrap();
            }
            if i < self.asks.len {
                let level = &self.asks.levels[i];
                write!(f, "    Ask: {}@{}\n", level.price, level.qty).unwrap();
            } else {
                write!(f, "\t\t\t\t\n").unwrap();
            }
        }
        write!(f, "\n")
    }
}
