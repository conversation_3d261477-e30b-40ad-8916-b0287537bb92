use crate::encoding::agg_trades::FuturesAggTrade;

/// 聚合后的交易数据，按秒聚合
#[derive(Debug, <PERSON>lone, Copy)]
pub struct AggregatedTradeData {
    pub timestamp: u64,       // 秒级时间戳
    pub open: f64,            // 开盘价
    pub close: f64,           // 收盘价
    pub high: f64,            // 最高价
    pub low: f64,             // 最低价
    pub total_volume: f64,    // 总成交量
    pub sell_volume: f64,     // 卖出成交量
    pub buy_volume: f64,      // 买入成交量
    pub trade_count: u64,     // 交易次数
    pub last_trade_time: u64, // 最后交易时间
    pub is_initialized: bool, // 是否已经初始化
}

impl Default for AggregatedTradeData {
    fn default() -> Self {
        Self {
            timestamp: 0,
            open: 0.0,
            close: 0.0,
            high: 0.0,
            low: 0.0,
            total_volume: 0.0,
            sell_volume: 0.0,
            buy_volume: 0.0,
            trade_count: 0,
            last_trade_time: 0,
            is_initialized: false,
        }
    }
}

impl AggregatedTradeData {
    /// 创建新的聚合数据
    pub fn new() -> Self {
        Self {
            timestamp: 0,
            open: 0.0,
            close: 0.0,
            high: 0.0,
            low: 0.0,
            total_volume: 0.0,
            sell_volume: 0.0,
            buy_volume: 0.0,
            trade_count: 0,
            last_trade_time: 0,
            is_initialized: false,
        }
    }

    pub fn update(
        &mut self,
        price: f64,
        quantity: f64,
        is_buyer_maker: bool,
        trade_count: u64,
        trade_time: u64,
    ) {
        // 如果是第一次更新，设置开盘价、最高价和最低价
        if !self.is_initialized {
            self.open = price;
            self.high = price;
            self.low = price;
            self.is_initialized = true;
        }

        self.close = price;
        self.high = self.high.max(price);
        self.low = self.low.min(price);
        self.total_volume += quantity;
        self.trade_count += trade_count;
        self.last_trade_time = self.last_trade_time.max(trade_time);

        if is_buyer_maker {
            self.sell_volume += quantity;
        } else {
            self.buy_volume += quantity;
        }
    }
}

/// 按symbol分组的聚合数据
#[derive(Debug, Clone)]
pub struct SymbolAggregations<const MAX_AGGS: usize> {
    pub aggregations: [Option<AggregatedTradeData>; MAX_AGGS],
    pub begin: usize,
    pub end: usize,
}

impl<const MAX_AGGS: usize> SymbolAggregations<MAX_AGGS> {
    /// 创建新的symbol聚合器
    pub fn new() -> Self {
        Self {
            aggregations: [None; MAX_AGGS],
            begin: 0,
            end: 0,
        }
    }

    pub fn add_or_update(
        &mut self,
        timestamp: u64,
        price: f64,
        quantity: f64,
        is_buyer_maker: bool,
        trade_count: u64,
        trade_time: u64,
    ) {
        if self.aggregations[self.end].is_none() {
            let mut new_agg = AggregatedTradeData::new();
            new_agg.timestamp = timestamp;
            self.aggregations[self.end] = Some(new_agg);
        }
        let last_bar = self.aggregations[self.end].as_mut().unwrap();
        if last_bar.timestamp == timestamp {
            last_bar.update(price, quantity, is_buyer_maker, trade_count, trade_time);
            return;
        }

        // 时间戳不同，需要创建新的bar
        self.end = (self.end + 1) % MAX_AGGS;

        // 如果缓冲区满了，移动begin指针
        if self.end == self.begin {
            self.begin = (self.begin + 1) % MAX_AGGS;
        }

        let mut new_agg = AggregatedTradeData::new();
        new_agg.timestamp = timestamp;
        self.aggregations[self.end] = Some(new_agg);
        self.aggregations[self.end].as_mut().unwrap().update(
            price,
            quantity,
            is_buyer_maker,
            trade_count,
            trade_time,
        );
    }

    pub fn len(&self) -> usize {
        if self.begin <= self.end {
            if self.begin == self.end {
                // 如果begin == end，检查是否有数据
                if self.aggregations[self.begin].is_some() {
                    1
                } else {
                    0
                }
            } else {
                self.end - self.begin + 1
            }
        } else {
            // 处理循环情况：begin > end
            if self.aggregations[self.begin].is_some() {
                MAX_AGGS - self.begin + self.end + 1
            } else {
                self.end + 1
            }
        }
    }

    pub fn get_recent_n_bar_avg_net_volume(&self, n: usize) -> Option<f64> {
        if self.len() < n {
            return None;
        }
        let mut total_buy_volume = 0.0;
        let mut total_sell_volume = 0.0;
        let mut end = self.end;
        let mut valid_count = 0;
        let mut processed = 0;

        while processed < self.len() && valid_count < n {
            if let Some(agg) = &self.aggregations[end] {
                total_buy_volume += agg.buy_volume;
                total_sell_volume += agg.sell_volume;
                valid_count += 1;
            }
            processed += 1;
            end = if end == 0 { MAX_AGGS - 1 } else { end - 1 };
        }
        Some((total_buy_volume - total_sell_volume) / n as f64)
    }

    pub fn get_recent_n_bar_avg_trade_vol(&self, n: usize) -> Option<f64> {
        if self.len() < n {
            return None;
        }
        let mut total_volume = 0.0;
        let mut end = self.end;
        let mut valid_count = 0;
        let mut processed = 0;

        while processed < self.len() && valid_count < n {
            if let Some(agg) = &self.aggregations[end] {
                total_volume += agg.total_volume;
                valid_count += 1;
            }
            processed += 1;
            end = if end == 0 { MAX_AGGS - 1 } else { end - 1 };
        }
        Some(total_volume / n as f64)
    }

    pub fn get_recent_n_bar_price_open_close_avg_change_rate(&self, n: usize) -> Option<f64> {
        if self.len() < n {
            return None;
        }

        // 找到最近n个bar中的第一个和最后一个
        let mut start = self.begin;

        // 如果n小于总长度，需要调整start位置
        if n < self.len() {
            start = (self.end + MAX_AGGS - n + 1) % MAX_AGGS;
        }

        let open_price = self.aggregations[start].as_ref()?.open;
        let close_price = self.aggregations[self.end].as_ref()?.close;

        if open_price == 0.0 {
            return None; // 避免除零错误
        }

        let change_rate = (close_price - open_price) / open_price;
        Some(change_rate / n as f64)
    }

    pub fn get_recent_n_bar_price_high_low_avg_change_rate(&self, n: usize) -> Option<f64> {
        if self.len() < n {
            return None;
        }

        let mut high_price: f64 = f64::NEG_INFINITY;
        let mut low_price: f64 = f64::INFINITY;
        let mut end = self.end;
        let mut valid_count = 0;
        let mut processed = 0;

        while processed < self.len() && valid_count < n {
            if let Some(agg) = &self.aggregations[end] {
                high_price = high_price.max(agg.high);
                low_price = low_price.min(agg.low);
                valid_count += 1;
            }
            processed += 1;
            end = if end == 0 { MAX_AGGS - 1 } else { end - 1 };
        }

        if low_price == f64::INFINITY || low_price == 0.0 {
            return None; // 避免除零错误
        }

        let change_rate = (high_price - low_price) / low_price;
        Some(change_rate / n as f64)
    }

    pub fn get_recent_n_bar_vol_surge_rate(&self, n: usize) -> Option<f64> {
        if self.len() < n {
            return None;
        }

        let end = self.end;
        let start = if end < n - 1 {
            self.end + MAX_AGGS - n + 1
        } else {
            end - n + 1
        };
        match (self.aggregations[start], self.aggregations[end]) {
            (Some(start_agg), Some(end_agg)) => {
                let start_vol = start_agg.total_volume;
                let end_vol = end_agg.total_volume;
                let change_rate = (end_vol - start_vol) / start_vol;
                Some(change_rate / n as f64)
            }
            _ => None,
        }
    }
}

pub struct AggFeature {
    pub bar_count: usize,
    pub avg_net_volume: f64,
    pub avg_trade_vol: f64,
    pub price_open_close_avg_change_rate: f64,
    pub price_high_low_avg_change_rate: f64,
    pub vol_surge_rate: f64,
}

pub struct FuturesAggTradeAggregator<const MAX_AGGS: usize> {
    aggregations: Option<SymbolAggregations<MAX_AGGS>>,
    pub last_trade: Option<FuturesAggTrade>,
}

impl<'a, const MAX_AGGS: usize> FuturesAggTradeAggregator<MAX_AGGS> {
    /// 创建新的聚合器
    pub fn new() -> Self {
        Self {
            aggregations: None,
            last_trade: None,
        }
    }

    pub fn get_last_trade(&self) -> Option<&FuturesAggTrade> {
        self.last_trade.as_ref()
    }

    /// 添加一个futures aggtrade到聚合器
    pub fn add_trades(&mut self, trade: &FuturesAggTrade) {
        let second_timestamp = trade.event_time;
        self.last_trade = Some(trade.clone());

        let trade_count = if trade.last_trade_id >= trade.first_trade_id {
            trade.last_trade_id - trade.first_trade_id + 1
        } else {
            1
        };

        match self.aggregations {
            Some(ref mut symbol_aggs) => {
                symbol_aggs.add_or_update(
                    second_timestamp,
                    trade.price,
                    trade.quantity,
                    trade.is_buyer_maker,
                    trade_count,
                    trade.trade_time,
                );
            }
            None => {
                self.aggregations = Some(SymbolAggregations::new());
                self.aggregations.as_mut().unwrap().add_or_update(
                    second_timestamp,
                    trade.price,
                    trade.quantity,
                    trade.is_buyer_maker,
                    trade_count,
                    trade.trade_time,
                );
            }
        }
    }

    pub fn get_agg_features(&self, bar_count: usize) -> Option<AggFeature> {
        if self.aggregations.is_none() {
            crate::info!("get_agg_features: aggregations is none");
            return None;
        }
        let symbol_aggs = self.aggregations.as_ref().unwrap();

        let avg_net_volume = symbol_aggs.get_recent_n_bar_avg_net_volume(bar_count);
        let avg_trade_vol = symbol_aggs.get_recent_n_bar_avg_trade_vol(bar_count);
        let price_open_close_avg_change_rate =
            symbol_aggs.get_recent_n_bar_price_open_close_avg_change_rate(bar_count);
        let price_high_low_avg_change_rate =
            symbol_aggs.get_recent_n_bar_price_high_low_avg_change_rate(bar_count);
        let vol_surge_rate = symbol_aggs.get_recent_n_bar_vol_surge_rate(bar_count);

        let avg_net_volume = avg_net_volume?;
        let avg_trade_vol = avg_trade_vol?;
        let price_open_close_avg_change_rate = price_open_close_avg_change_rate?;
        let price_high_low_avg_change_rate = price_high_low_avg_change_rate?;
        let vol_surge_rate = vol_surge_rate?;

        Some(AggFeature {
            bar_count,
            avg_net_volume,
            avg_trade_vol,
            price_open_close_avg_change_rate,
            price_high_low_avg_change_rate,
            vol_surge_rate,
        })
    }
}

impl<const MAX_AGGS: usize> Default for FuturesAggTradeAggregator<MAX_AGGS> {
    fn default() -> Self {
        Self::new()
    }
}
