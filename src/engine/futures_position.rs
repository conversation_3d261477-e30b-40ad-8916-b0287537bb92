use crate::{encoding::futures_order::OrderSide, utils::perf::system_now_in_us};

pub struct FuturesPosition {
    pub position: f64,
    pub entry_price: f64,
    pub unrealized_pnl: f64,
    pub current_bid_price: f64,
    pub current_ask_price: f64,
    pub last_close_time: u64,
}

impl FuturesPosition {
    pub fn new() -> Self {
        Self {
            position: 0.0,
            entry_price: 0.0,
            unrealized_pnl: 0.0,
            current_bid_price: 0.0,
            current_ask_price: 0.0,
            last_close_time: 0,
        }
    }

    pub fn add_trade(&mut self, price: f64, quantity: f64, side: OrderSide) {
        let signed_qty = quantity * side.sign();
        if self.entry_price == 0.0 || (self.position + signed_qty).abs() < 0.0000001 {
            self.entry_price = price;
        } else {
            self.entry_price = (self.entry_price * self.position + price * signed_qty)
                / (self.position + signed_qty);
        }
        self.position += signed_qty;
        crate::info!(
            "add trade: {} {} {} {} {}",
            price,
            quantity,
            side.to_string(),
            self.position,
            self.entry_price
        );
    }

    pub fn update_current_price(&mut self, bid_price: f64, ask_price: f64) {
        let price = if self.position > 0.0 {
            ask_price
        } else {
            bid_price
        };
        let new_unrealized_pnl = (price - self.entry_price) * self.position;
        self.current_bid_price = bid_price;
        self.current_ask_price = ask_price;
        if new_unrealized_pnl != self.unrealized_pnl && self.position.abs() > 0.0001 {
            crate::debug!(
                "price: {}, entry_price: {}, position: {}, unrealized pnl: {}",
                price,
                self.entry_price,
                self.position,
                new_unrealized_pnl
            );
            self.unrealized_pnl = new_unrealized_pnl;
        }
    }

    pub fn try_close_position(&mut self) -> Option<(f64, f64)> {
        let now = system_now_in_us();
        if now - self.last_close_time < 1000 * 1000 {
            return None;
        }
        if self.position.abs() <= 0.001 {
            return None;
        }
        let price = if self.position > 0.0 {
            self.current_ask_price - 0.001
        } else {
            self.current_bid_price + 0.001
        };
        let taker_fee = self.position.abs() * price * 0.0004;
        if self.unrealized_pnl > taker_fee * 30.0 {
            crate::info!(
                "take profit: price: {}, position: {}, amout: {}, unrealized pnl: {}",
                price,
                self.position,
                self.position * price,
                self.unrealized_pnl
            );
            self.last_close_time = now;

            Some((price, self.position))
        } else if self.unrealized_pnl < 0.0 && self.unrealized_pnl.abs() > taker_fee * 10.0 {
            crate::info!(
                "stop loss: price: {}, position: {}, amout: {}, unrealized pnl: {}",
                price,
                self.position,
                self.position * price,
                self.unrealized_pnl
            );
            Some((price, self.position))
        } else {
            None
        }
    }
}
