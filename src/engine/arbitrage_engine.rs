use crate::{
    EdgeDirection, ORDER_FILTER_LOT_SIZE_INDEX, ORDER_FILTER_MIN_NOTIONAL_INDEX,
    ORDER_FILTER_MIN_ORDER_QTY_INDEX, ORDER_FILTERS, PREDEFINED_RINGS, TRADING_PAIR_RATES,
    TRADING_PAIR_TO_RING_INDEX, TradingPair, WebSocketHandle,
    encoding::{
        agg_trades::AggTrade,
        book_ticker::BookTicker,
        depth_snapshot::DepthSnapshot,
        depth_update::DepthUpdate,
        sbe::{SbeBookTicker, SbeDepthSnapshot, SbeDepthUpdate, SbeTrade},
    },
    engine::{
        arbitrage_engine_utils::{
            balance_by_dir, level_1_in_usdt, min_balance_in_ring, record_order_price,
            usdt_amount_to_base_quantity_by_dir,
        },
        monitor::CURRENT_RING_TS,
        token::{ORDER_TOKEN_1, ORDER_TOKEN_2, ORDER_TOKEN_3, ORDER_TOKEN_4},
        trade::{generate_cancel_order, generate_ioc_order},
        trading_pair::{
            ORDER_BOOKS, RING_ORDER_STATUS, TRADING_PAIR_QTY, TRADING_PAIR_RATE_EVENT_TIME,
            TRADING_PAIR_RATE_FROM, TRADING_PAIR_RATE_UPDATE_ID, TRADING_PAIR_RATE_UPDATE_TIME,
        },
    },
    error, trace,
    utils::{
        self,
        perf::{circles_to_ns, system_now_in_ms, system_now_in_us},
    },
};

const ORDER_MIN_AMOUNT: f64 = 10.0f64;

pub static mut IS_ARBITRAGING: bool = false;
pub static mut CURRENT_INDEX: usize = 0;
pub static mut EXPECT_RATE: f64 = 0.0;
pub static mut ORDER_QUANTITIES: [f64; 4] = [0.0; 4];
pub static mut ORDER_PRICES: [f64; 4] = [0.0; 4];
// 0, ring ts, 1, timestamp, 2, ring index, 3, edge index
pub static mut PENDING_ORDERS: [(u64, u64, u16, u16); 6] = [(0, 0, 0, 0); 6];
pub static PENDING_ORDERS_LEN: usize = 5;

fn adjust_quantity(
    base_q: f64,
    quote_q: f64,
    min_qty: f64,
    step: f64,
    min_national: f64,
) -> Option<f64> {
    trace!(
        "base_q: {}, quote_q: {}, min_qty: {}, step: {}, min_national: {}",
        base_q, quote_q, min_qty, step, min_national
    );
    if base_q < min_qty {
        return None;
    }
    if quote_q < min_national {
        return None;
    }
    let steps = (base_q / step).floor();
    let adjusted = steps * step;
    if adjusted > min_qty {
        return Some(adjusted);
    }
    return Some(min_qty);
}

fn quote_to_base_quantity(quote: f64, pair: TradingPair) -> f64 {
    unsafe { quote * TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] }
}

fn base_to_quote_quantity(base: f64, pair: TradingPair) -> f64 {
    unsafe { base * TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] }
}

pub static mut USER_DATA_SUBSCRIBED: bool = false;
pub static mut TRADING_FEE_UPDATED: bool = true;
pub static mut RECONNECT_TIME: u64 = 0;

pub struct ArbitrageEngine {}

impl ArbitrageEngine {
    pub fn set_user_data_subscribed() {
        unsafe {
            USER_DATA_SUBSCRIBED = true;
        }
    }

    pub fn unset_user_data_subscribed() {
        unsafe {
            USER_DATA_SUBSCRIBED = false;
        }
    }

    pub fn set_reconnect_time(now: u64) {
        unsafe {
            RECONNECT_TIME = now;
        }
    }

    pub fn user_data_subscribed() -> bool {
        unsafe { USER_DATA_SUBSCRIBED }
    }

    pub fn set_trading_fee_updated() {
        unsafe {
            TRADING_FEE_UPDATED = true;
        }
    }

    pub fn get_trading_fee_updated() -> bool {
        unsafe { TRADING_FEE_UPDATED }
    }

    #[inline(always)]
    pub fn check_arbitrage_conditions(now: u64) -> bool {
        unsafe {
            if circles_to_ns(now - RECONNECT_TIME) < 2_200_000_000.0 {
                return false;
            }
            USER_DATA_SUBSCRIBED && TRADING_FEE_UPDATED && {
                circles_to_ns(now - CURRENT_RING_TS) > 60_000_000.0
            }
        }
    }

    pub fn cancel_pending_orders<const IN_LEN: usize, const OUT_LEN: usize>(
        handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>,
    ) {
        let now = system_now_in_ms();
        let mut pending_count = 0;
        let mut oldest_index = 0;
        let mut oldest_timestamp = now;
        for i in 0..PENDING_ORDERS_LEN {
            let (arb_ts, timestamp, ring_index, edge_index) = unsafe { PENDING_ORDERS[i] };
            if timestamp == 0 {
                continue;
            }
            if timestamp < oldest_timestamp {
                oldest_timestamp = timestamp;
                oldest_index = i;
            }
            pending_count += 1;
            if now - timestamp > 1800_000 {
                crate::info!(
                    "cancelling pending order, ts: {} ring_index: {} edge_index: {}",
                    timestamp,
                    ring_index,
                    edge_index,
                );
                let buf = handle.get_write_buf(ORDER_TOKEN_1).unwrap();
                generate_cancel_order(arb_ts, ring_index as usize, edge_index as usize, buf);
                handle.trigger_write(ORDER_TOKEN_1).unwrap();
                unsafe {
                    RING_ORDER_STATUS[ring_index as usize][edge_index as usize] = 2;
                    PENDING_ORDERS[i] = (0, 0, 0, 0);
                }
                pending_count -= 1;
            }
        }
        if pending_count == PENDING_ORDERS_LEN {
            let buf = handle.get_write_buf(ORDER_TOKEN_1).unwrap();
            let (arb_ts, _, ring_index, edge_index) = unsafe { PENDING_ORDERS[oldest_index] };
            generate_cancel_order(arb_ts, ring_index as usize, edge_index as usize, buf);
            handle.trigger_write(ORDER_TOKEN_1).unwrap();
            unsafe {
                RING_ORDER_STATUS[ring_index as usize][edge_index as usize] = 2;
                PENDING_ORDERS[oldest_index] = (0, 0, 0, 0);
            }
        }
    }

    pub fn record_pending_order(arb_ts: u64, curr_ts: u64, ring_index: usize, edge_index: usize) {
        unsafe {
            for i in 0..PENDING_ORDERS_LEN {
                if PENDING_ORDERS[i].0 == 0 {
                    PENDING_ORDERS[i] = (arb_ts, curr_ts, ring_index as u16, edge_index as u16);
                    crate::info_unsafe!(
                        "recording pending order: {} {} {} {}",
                        arb_ts,
                        curr_ts,
                        ring_index,
                        edge_index
                    );
                    break;
                }
            }
        }
    }

    pub fn cancel_pending_order(arb_ts: u64, ring_index: u16, edge_index: u16) {
        unsafe {
            for i in 0..PENDING_ORDERS_LEN {
                if PENDING_ORDERS[i].0 == arb_ts
                    && PENDING_ORDERS[i].2 == ring_index
                    && PENDING_ORDERS[i].3 == edge_index
                {
                    crate::info_unsafe!(
                        "delete pending order: {} {} {} {}",
                        arb_ts,
                        PENDING_ORDERS[i].1,
                        ring_index,
                        edge_index,
                    );
                    PENDING_ORDERS[i] = (0, 0, 0, 0);
                    break;
                }
            }
        }
    }

    // #[perf_macro::measure]
    #[inline(always)]
    pub fn place_orders<const IN_LEN: usize, const OUT_LEN: usize>(
        ring_index: usize,
        now: u64,
        handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>,
    ) {
        unsafe {
            Self::cancel_pending_orders(handle);
            CURRENT_INDEX = ring_index;
        }
        let ring = PREDEFINED_RINGS[ring_index];
        let len = ring.len();

        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        match len {
            3 => {
                let buf = handle.get_write_buf(ORDER_TOKEN_1).unwrap();
                generate_ioc_order(ring, ring_index, now, timestamp, buf, 0);
                let buf = handle.get_write_buf(ORDER_TOKEN_2).unwrap();
                generate_ioc_order(ring, ring_index, now, timestamp, buf, 1);
                let buf = handle.get_write_buf(ORDER_TOKEN_3).unwrap();
                generate_ioc_order(ring, ring_index, now, timestamp, buf, 2);
                handle
                    .trigger_write_3(ORDER_TOKEN_1, ORDER_TOKEN_2, ORDER_TOKEN_3)
                    .unwrap();
            }
            4 => {
                let buf = handle.get_write_buf(ORDER_TOKEN_1).unwrap();
                generate_ioc_order(ring, ring_index, now, timestamp, buf, 0);
                let buf = handle.get_write_buf(ORDER_TOKEN_2).unwrap();
                generate_ioc_order(ring, ring_index, now, timestamp, buf, 1);
                let buf = handle.get_write_buf(ORDER_TOKEN_3).unwrap();
                generate_ioc_order(ring, ring_index, now, timestamp, buf, 2);
                let buf = handle.get_write_buf(ORDER_TOKEN_4).unwrap();
                generate_ioc_order(ring, ring_index, now, timestamp, buf, 3);

                handle
                    .trigger_write_4(ORDER_TOKEN_1, ORDER_TOKEN_2, ORDER_TOKEN_3, ORDER_TOKEN_4)
                    .unwrap();
            }
            _ => {}
        }
        let end = utils::perf::now();
        let mut buf = ryu::Buffer::new();
        let latency_str = buf.format(circles_to_ns(end - now));
        error!("Total latency: {}ns", latency_str);
        unsafe {
            CURRENT_RING_TS = now;
        }
        let mut product = 1.0;
        for i in 0..PREDEFINED_RINGS[ring_index].len() {
            let (pair, dir) = PREDEFINED_RINGS[ring_index][i];
            let rate = unsafe { TRADING_PAIR_RATES[pair as usize][dir as usize] };
            product *= rate;
            let price = match dir {
                EdgeDirection::Forward => 1.0 / rate,
                EdgeDirection::Reverse => rate,
            };
            let mut price_buffer = ryu::Buffer::new();
            let price_str = price_buffer.format(price);
            let mut update_time_buffer = itoa::Buffer::new();
            let update_time_str =
                update_time_buffer.format(unsafe { TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] });
            let mut event_time_buffer = itoa::Buffer::new();
            let event_time_str =
                event_time_buffer.format(unsafe { TRADING_PAIR_RATE_EVENT_TIME[pair as usize] });
            let mut from_buffer = itoa::Buffer::new();
            let from_str = from_buffer.format(unsafe { TRADING_PAIR_RATE_FROM[pair as usize] });
            let qty = unsafe { TRADING_PAIR_QTY[pair as usize][dir as usize] };
            error!(
                "{}: dir: {} p: {:.12} q: {:.8} total_qty: {} u_ts: {} e_ts: {} from: {} ",
                pair.to_str(),
                dir.to_str(),
                price_str,
                unsafe { ORDER_QUANTITIES[i] },
                qty,
                update_time_str,
                event_time_str,
                from_str
            );
        }
        let mut ryu_buffer = ryu::Buffer::new();
        let max_product_str = ryu_buffer.format(product);
        error!("Expect rate: {}", max_product_str);
    }

    pub fn update_rate_by_ws_book_ticker(book_ticker: &BookTicker) -> bool {
        let pair: TradingPair = book_ticker.symbol.into();
        unsafe {
            // 如果已经由trade标记为无效，则暂时不用json bbo了
            if TRADING_PAIR_RATE_FROM[pair as usize] == 0 {
                if book_ticker.update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                    TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = book_ticker.update_id;
                }
                return false;
            }
            if book_ticker.update_id <= TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                return false;
            }
            let now = system_now_in_us();
            TRADING_PAIR_RATE_FROM[pair as usize] = 4;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                1.0 / book_ticker.ask_price;
            TRADING_PAIR_QTY[pair as usize][EdgeDirection::Forward as usize] = book_ticker.ask_qty;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] =
                book_ticker.bid_price;
            TRADING_PAIR_QTY[pair as usize][EdgeDirection::Reverse as usize] = book_ticker.bid_qty;
            TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
            TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = book_ticker.update_id;
        }
        true
    }

    // #[perf_macro::measure]
    #[inline(always)]
    pub fn update_rate(book_ticker: &SbeBookTicker) -> bool {
        let pair: TradingPair = book_ticker.symbol.into();
        unsafe {
            if book_ticker.book_update_id <= TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                return false;
            }
            TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = book_ticker.book_update_id;
            let now = system_now_in_us();
            if (now - book_ticker.event_time) > 900 {
                TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                return false;
            }
            TRADING_PAIR_RATE_FROM[pair as usize] = 1;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                1.0 / book_ticker.ask_price;
            TRADING_PAIR_QTY[pair as usize][EdgeDirection::Forward as usize] = book_ticker.ask_qty;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] =
                book_ticker.bid_price;
            TRADING_PAIR_QTY[pair as usize][EdgeDirection::Reverse as usize] = book_ticker.bid_qty;
            TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = book_ticker.event_time;
            TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
            TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = book_ticker.book_update_id;
        }
        true
    }

    pub fn update_orderbook_by_updates(diff: &SbeDepthUpdate) -> bool {
        unsafe {
            let now = system_now_in_us();
            let pair: TradingPair = diff.symbol.into();
            let order_book_ptr = std::ptr::addr_of_mut!(ORDER_BOOKS);
            if !(*order_book_ptr)[pair as usize].apply_diff(
                diff.final_update_id,
                &diff.bid_updates,
                &diff.ask_updates,
                diff.event_time,
            ) {
                return false;
            }
            if diff.final_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                if (now - diff.event_time) > 3_000 {
                    TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                    return false;
                }
                TRADING_PAIR_RATE_FROM[pair as usize] = 2;
                TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = diff.final_update_id;
                TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = diff.event_time;
                TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
                if let Some(bid) = (*order_book_ptr)[pair as usize].best_bid() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid.price;
                }
                if let Some(ask) = (*order_book_ptr)[pair as usize].best_ask() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                        1.0 / ask.price;
                }
                return true;
            }
        }
        return false;
    }

    pub fn update_orderbook_by_ws_updates(diff: &DepthUpdate) -> bool {
        unsafe {
            let now = system_now_in_us();
            let pair: TradingPair = diff.symbol.into();
            let order_book_ptr = std::ptr::addr_of_mut!(ORDER_BOOKS);
            if !(*order_book_ptr)[pair as usize].apply_diff(
                diff.final_update_id,
                &diff.bid_updates,
                &diff.ask_updates,
                diff.event_time,
            ) {
                return false;
            }
            if diff.final_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                if (now - diff.event_time) > 3_000 {
                    TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                    return false;
                }
                TRADING_PAIR_RATE_FROM[pair as usize] = 5;
                TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = diff.final_update_id;
                TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = diff.event_time;
                TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
                if let Some(bid) = (*order_book_ptr)[pair as usize].best_bid() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid.price;
                }
                if let Some(ask) = (*order_book_ptr)[pair as usize].best_ask() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                        1.0 / ask.price;
                }
                return true;
            }
        }
        return false;
    }

    pub fn update_orderbook_by_snapshot(snapshot: &SbeDepthSnapshot) -> bool {
        unsafe {
            let pair: TradingPair = snapshot.symbol.into();
            let order_book_ptr = std::ptr::addr_of_mut!(ORDER_BOOKS);
            if !(*order_book_ptr)[pair as usize].apply_snapshot(
                snapshot.last_update_id,
                &snapshot.bids,
                &snapshot.asks,
                snapshot.event_time,
            ) {
                return false;
            }
            if snapshot.last_update_id <= TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                return false;
            }
            TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = snapshot.last_update_id;
            let now = system_now_in_us();
            if (now - snapshot.event_time) > 2_000 {
                TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                return false;
            }
            TRADING_PAIR_RATE_FROM[pair as usize] = 3;
            TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = snapshot.event_time;
            TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
            if let Some(bid) = (*order_book_ptr)[pair as usize].best_bid() {
                TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid.price;
                TRADING_PAIR_QTY[pair as usize][EdgeDirection::Reverse as usize] = bid.qty;
            }
            if let Some(ask) = (*order_book_ptr)[pair as usize].best_ask() {
                TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                    1.0 / ask.price;
                TRADING_PAIR_QTY[pair as usize][EdgeDirection::Forward as usize] = ask.qty;
            }
            true
        }
    }

    pub fn update_orderbook_by_ws_snapshot(snapshot: &DepthSnapshot) -> bool {
        unsafe {
            let now = system_now_in_us();
            let pair: TradingPair = TradingPair::from_lower_case(snapshot.symbol);
            let order_book_ptr = std::ptr::addr_of_mut!(ORDER_BOOKS);
            if !(*order_book_ptr)[pair as usize].apply_snapshot(
                snapshot.last_update_id,
                &snapshot.bids,
                &snapshot.asks,
                0, // no event time
            ) {
                return false;
            }
            if snapshot.last_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                TRADING_PAIR_RATE_FROM[pair as usize] = 6;
                TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = snapshot.last_update_id;
                TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
                if let Some(bid) = (*order_book_ptr)[pair as usize].best_bid() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid.price;
                }
                if let Some(ask) = (*order_book_ptr)[pair as usize].best_ask() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                        1.0 / ask.price;
                }
                return true;
            }
            false
        }
    }

    pub fn update_rate_by_trades(trade: SbeTrade) {
        let pair: TradingPair = trade.symbol.into();
        // 无脑信任trade比bbo或者depth更快
        // trade last trade price 如果不在[bid1, asks1]之间，则标记为 invalid
        let price = trade.last_trade_price;
        unsafe {
            let bid1 = TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize];
            let ask1 = 1.0 / TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize];
            if !(price > bid1 && price < ask1) {
                TRADING_PAIR_RATE_FROM[pair as usize] = 0;
            }
            TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = trade.event_time;
        }
    }

    pub fn update_rate_by_agg_trades(trade: AggTrade) {
        let pair: TradingPair = trade.symbol.into();
        let update_time = unsafe { TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] };

        // 统一使用event_time对比
        if trade.event_time > update_time {
            // trade last trade priru 如果不在[bid1, asks1]之间，则标记为 invalid
            let price = trade.price;
            unsafe {
                let bid1 = TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize];
                let ask1 = 1.0 / TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize];
                if !(price > bid1 && price < ask1) {
                    TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                }
            }
        }
    }

    // #[perf_macro::measure]
    #[inline(always)]
    pub fn compute_orders(circle_index: usize) -> Option<usize> {
        let min_balance = min_balance_in_ring(circle_index);
        if min_balance < ORDER_MIN_AMOUNT {
            crate::info!("min balance too low: {}", min_balance);
            return None;
        }
        let ring = PREDEFINED_RINGS[circle_index];
        let balance_usdt_amount = min_balance * 0.9;
        let level_1_amount = level_1_in_usdt(ring[0].0, ring[0].1);
        let order_usdt_amount = if level_1_amount * 0.7 > balance_usdt_amount {
            balance_usdt_amount
        } else {
            level_1_amount * 0.7
        };
        crate::info!("order_usdt_amount: {}", order_usdt_amount);
        for try_count in 0..10 {
            let mut last_asset_q = 0.0f64;
            let mut i = 0;
            while i < ring.len() {
                let (pair, direction) = ring[i];
                let order_filters = ORDER_FILTERS[pair as usize];
                if i == 0 {
                    if try_count == 0 {
                        let init_base_q =
                            usdt_amount_to_base_quantity_by_dir(order_usdt_amount, pair);
                        let base_asset_q_adjusted = adjust_quantity(
                            init_base_q,
                            order_usdt_amount,
                            order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                            order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                            order_filters[ORDER_FILTER_MIN_NOTIONAL_INDEX],
                        )?;
                        unsafe {
                            ORDER_QUANTITIES[i] = base_asset_q_adjusted;
                        }
                        last_asset_q = match direction {
                            EdgeDirection::Forward => base_asset_q_adjusted,
                            EdgeDirection::Reverse => {
                                base_to_quote_quantity(base_asset_q_adjusted, pair)
                            }
                        };
                    } else {
                        unsafe {
                            ORDER_QUANTITIES[i] += order_filters[ORDER_FILTER_LOT_SIZE_INDEX];
                            match direction {
                                EdgeDirection::Forward => {
                                    last_asset_q = ORDER_QUANTITIES[i];
                                }
                                EdgeDirection::Reverse => {
                                    last_asset_q =
                                        base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                }
                            }
                        }
                    }
                    i += 1;
                    continue;
                }
                let (base_q, quote_q) = match direction {
                    EdgeDirection::Forward => {
                        (quote_to_base_quantity(last_asset_q, pair), last_asset_q)
                    }
                    EdgeDirection::Reverse => {
                        (last_asset_q, base_to_quote_quantity(last_asset_q, pair))
                    }
                };
                let q = match adjust_quantity(
                    base_q,
                    quote_q,
                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                    order_filters[ORDER_FILTER_MIN_NOTIONAL_INDEX],
                ) {
                    Some(r) => r,
                    None => break,
                };
                unsafe {
                    ORDER_QUANTITIES[i] = q;
                }
                last_asset_q = match direction {
                    EdgeDirection::Forward => q,
                    EdgeDirection::Reverse => base_to_quote_quantity(q, pair),
                };
                i += 1;
            }
            if i == ring.len() {
                return Some(i);
            }
        }
        None
    }

    // #[perf_macro::measure]
    #[inline(always)]
    pub fn check_arbitrage(now: u64, pair: TradingPair) -> Option<usize> {
        if !Self::check_arbitrage_conditions(now) {
            return None;
        }
        let ring_indices = TRADING_PAIR_TO_RING_INDEX[pair as usize];
        let mut max_product = 0.0f64;
        let mut result: usize = 0;
        for index in ring_indices {
            let ring = PREDEFINED_RINGS[*index as usize];
            let mut product = 1.0;

            for &(pair, dir) in ring {
                if unsafe { TRADING_PAIR_RATE_FROM[pair as usize] } == 0 {
                    product = 0.0;
                    break;
                }
                let rate = unsafe { TRADING_PAIR_RATES[pair as usize][dir as usize] };
                if rate == 0.0 {
                    product = 0.0;
                    break;
                }
                let balance = balance_by_dir(pair, dir);
                if balance < ORDER_MIN_AMOUNT {
                    product = 0.0;
                    trace!("balance too low: {} {}", pair.to_str(), balance);
                    break;
                }
                let amount = level_1_in_usdt(pair, dir);
                if amount < 1000.0 {
                    trace!("amount too low: {} {}", pair.to_str(), amount);
                    product = 0.0;
                    break;
                }
                product *= rate;
            }
            if product > max_product {
                result = *index as usize;
                max_product = product;
            }
        }
        trace!("max product: {}", max_product);
        if max_product < 1.003 && max_product > 1.001 {
            unsafe { EXPECT_RATE = max_product }
            record_order_price(result);
            Some(result)
        } else {
            None
        }
    }
}
